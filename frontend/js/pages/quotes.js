/**
 * Quotes List Page Controller
 * Independent implementation for /quotes/ page
 * Uses same APIs as other pages but with quotes-specific logic
 * Architecture pattern consistent with Authors, Categories, and Sources pages
 */

// Page state - using independent namespace to avoid conflicts
const quotesListPageState = {
    // Data state
    allQuotes: [],
    displayedQuotes: [],
    filteredQuotes: [],
    
    // Pagination state
    currentPage: 1,
    pageSize: 20,  // List view: 20 quotes per page
    totalPages: 0,
    totalCount: 0,
    
    // UI state
    isLoading: false,
    searchQuery: '',
    sortOrder: 'newest',
    filterCategory: '',
    
    // Performance state
    loadedCount: 0,
    maxQuotes: 1000,
    hasMore: true
};

/**
 * Page initialization function
 * Called by PageRouter: initQuotesListPage()
 */
async function initQuotesListPage(params) {
    try {
        console.log('🚀 Initializing Quotes List Page...');
        console.log('📋 Params received:', params);
        console.log('🔍 Available global objects:', {
            ApiClient: !!window.ApiClient,
            ComponentLoader: !!window.ComponentLoader,
            PageRouter: !!window.PageRouter,
            UrlHandler: !!window.Url<PERSON><PERSON><PERSON>,
            QuoteCardComponent: !!window.QuoteCardComponent
        });

        // Extract page number from params or URL
        const urlParams = new URLSearchParams(window.location.search);
        const pageFromUrl = parseInt(urlParams.get('page')) || 1;
        quotesListPageState.currentPage = pageFromUrl;

        // Show loading state
        showLoadingState();
        console.log('⏳ Loading state shown');

        // Load page components
        try {
            await loadPageComponents();
            console.log('✅ Page components loaded');
        } catch (componentError) {
            console.warn('⚠️ Component loading failed, continuing anyway:', componentError);
        }

        // Load quotes data
        try {
            await loadQuotesData();
            console.log('✅ Quotes data loaded');
        } catch (dataError) {
            console.error('❌ Quotes data loading failed:', dataError);
            throw dataError; // This is critical, so we should fail
        }

        // Initialize UI controls
        try {
            initializeControls();
            console.log('✅ UI controls initialized');
        } catch (controlsError) {
            console.warn('⚠️ UI controls initialization failed:', controlsError);
        }

        // Update page metadata
        updatePageMetadata();
        console.log('✅ Page metadata updated');

        // Hide loading state and show content
        hideLoadingState();
        console.log('✅ Quotes List Page initialization complete');

    } catch (error) {
        console.error('❌ Quotes List Page initialization failed:', error);
        showErrorState(error.message);
        throw error;
    }
}

/**
 * Load page components
 */
async function loadPageComponents() {
    if (window.ComponentLoader) {
        try {
            // Load navigation component
            await window.ComponentLoader.loadComponent('navigation-container', 'navigation');
            
            // Load footer component
            await window.ComponentLoader.loadComponent('footer-container', 'footer');
            
            console.log('✅ Core components loaded successfully');
        } catch (error) {
            console.warn('⚠️ Some components failed to load:', error);
        }
    } else {
        console.warn('⚠️ ComponentLoader not available');
    }
}

/**
 * Load quotes data
 */
async function loadQuotesData() {
    try {
        // Use production API data
        if (window.ApiClient) {
            window.ApiClient.useMockData = false;
        }

        // Get quotes with pagination
        const quotesData = await window.ApiClient.getQuotes(
            quotesListPageState.currentPage, 
            quotesListPageState.pageSize
        );

        // Update state
        quotesListPageState.allQuotes = quotesData.quotes || [];
        quotesListPageState.displayedQuotes = quotesData.quotes || [];
        quotesListPageState.totalCount = quotesData.totalCount || 0;
        quotesListPageState.totalPages = Math.ceil(quotesListPageState.totalCount / quotesListPageState.pageSize);

        console.log('📊 Quotes data loaded:', {
            quotes: quotesListPageState.allQuotes.length,
            totalCount: quotesListPageState.totalCount,
            currentPage: quotesListPageState.currentPage,
            totalPages: quotesListPageState.totalPages
        });

        // Render quotes
        renderQuotes();

        // Update pagination
        updatePagination();

        return quotesData;
    } catch (error) {
        console.error('❌ Error loading quotes data:', error);
        throw error;
    }
}

/**
 * Render quotes list
 */
function renderQuotes() {
    if (!window.QuoteCardComponent) {
        console.error('❌ QuoteCardComponent not available');
        return;
    }

    // Use the quote card component to render quotes with navigation enabled
    window.QuoteCardComponent.renderList(quotesListPageState.displayedQuotes, 'quotes-grid', {
        showAuthorAvatar: false,
        showActions: false,
        highlightCurrentCategory: false,
        hideEmptyAvatar: true,
        enableNavigation: true, // Enable click navigation to quote details
        emptyMessage: 'No quotes found. Try adjusting your search or filter criteria.'
    });

    console.log(`✅ Rendered ${quotesListPageState.displayedQuotes.length} quotes`);
}

/**
 * Initialize UI controls
 */
function initializeControls() {
    // Initialize search functionality
    const searchInput = document.getElementById('quote-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
        console.log('✅ Search input initialized');
    }

    // Initialize filter functionality
    const filterSelect = document.getElementById('quote-filter');
    if (filterSelect) {
        filterSelect.addEventListener('change', handleFilter);
        console.log('✅ Filter select initialized');
    }
}

/**
 * Handle search input
 */
function handleSearch(event) {
    const query = event.target.value.trim().toLowerCase();
    quotesListPageState.searchQuery = query;
    
    console.log('🔍 Search query:', query);
    
    // Filter quotes based on search query
    filterQuotes();
}

/**
 * Handle category filter
 */
function handleFilter(event) {
    const category = event.target.value;
    quotesListPageState.filterCategory = category;
    
    console.log('🏷️ Filter category:', category);
    
    // Filter quotes based on category
    filterQuotes();
}

/**
 * Filter quotes based on search and category
 */
function filterQuotes() {
    let filtered = [...quotesListPageState.allQuotes];
    
    // Apply search filter
    if (quotesListPageState.searchQuery) {
        filtered = filtered.filter(quote => 
            quote.content.toLowerCase().includes(quotesListPageState.searchQuery) ||
            quote.author.name.toLowerCase().includes(quotesListPageState.searchQuery)
        );
    }
    
    // Apply category filter
    if (quotesListPageState.filterCategory) {
        filtered = filtered.filter(quote => 
            quote.categories.some(cat => 
                cat.name.toLowerCase() === quotesListPageState.filterCategory.toLowerCase()
            )
        );
    }
    
    quotesListPageState.filteredQuotes = filtered;
    quotesListPageState.displayedQuotes = filtered;
    
    console.log(`🔍 Filtered ${filtered.length} quotes from ${quotesListPageState.allQuotes.length} total`);
    
    // Re-render quotes
    renderQuotes();
}

/**
 * Update pagination
 */
function updatePagination() {
    const paginationContainer = document.getElementById('pagination-container');
    if (!paginationContainer) {
        console.warn('⚠️ Pagination container not found');
        return;
    }

    // Simple pagination implementation
    if (quotesListPageState.totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }

    let paginationHTML = '<div class="flex justify-center items-center space-x-2">';
    
    // Previous button
    if (quotesListPageState.currentPage > 1) {
        paginationHTML += `<button onclick="goToPage(${quotesListPageState.currentPage - 1})" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors">Previous</button>`;
    }
    
    // Page numbers (show current page and a few around it)
    const startPage = Math.max(1, quotesListPageState.currentPage - 2);
    const endPage = Math.min(quotesListPageState.totalPages, quotesListPageState.currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const isActive = i === quotesListPageState.currentPage;
        const buttonClass = isActive 
            ? 'px-4 py-2 bg-yellow-500 text-white rounded font-semibold'
            : 'px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors';
        
        paginationHTML += `<button onclick="goToPage(${i})" class="${buttonClass}">${i}</button>`;
    }
    
    // Next button
    if (quotesListPageState.currentPage < quotesListPageState.totalPages) {
        paginationHTML += `<button onclick="goToPage(${quotesListPageState.currentPage + 1})" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors">Next</button>`;
    }
    
    paginationHTML += '</div>';
    
    paginationContainer.innerHTML = paginationHTML;
    console.log('✅ Pagination updated');
}

/**
 * Navigate to specific page
 */
function goToPage(page) {
    if (page < 1 || page > quotesListPageState.totalPages) {
        return;
    }
    
    quotesListPageState.currentPage = page;
    
    // Update URL with page parameter
    const url = new URL(window.location);
    if (page === 1) {
        url.searchParams.delete('page');
    } else {
        url.searchParams.set('page', page);
    }
    
    // Use UrlHandler for navigation if available
    if (window.UrlHandler && window.UrlHandler.navigateTo) {
        window.UrlHandler.navigateTo(url.pathname + url.search);
    } else {
        window.history.pushState({}, '', url);
        // Reload data for new page
        loadQuotesData();
    }
}

/**
 * Update page metadata
 */
function updatePageMetadata() {
    const pageTitle = quotesListPageState.currentPage > 1
        ? `Famous Quotes Collection - Page ${quotesListPageState.currentPage} | Quotese.com`
        : 'Famous Quotes Collection | Quotese.com';

    document.title = pageTitle;

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
        metaDescription.content = `Browse our complete collection of famous quotes. Find inspiration and wisdom from great minds. Page ${quotesListPageState.currentPage} of ${quotesListPageState.totalPages}.`;
    }
}

/**
 * Show loading state
 */
function showLoadingState() {
    quotesListPageState.isLoading = true;

    const quotesGrid = document.getElementById('quotes-grid');
    if (quotesGrid) {
        quotesGrid.innerHTML = `
            <div class="flex justify-center py-12">
                <div class="loading-spinner" role="status">
                    <span class="sr-only">Loading quotes...</span>
                </div>
            </div>
        `;
    }
}

/**
 * Hide loading state
 */
function hideLoadingState() {
    quotesListPageState.isLoading = false;
}

/**
 * Show error state
 */
function showErrorState(message) {
    const quotesGrid = document.getElementById('quotes-grid');
    if (quotesGrid) {
        quotesGrid.innerHTML = `
            <div class="text-center py-12">
                <div class="text-red-500 text-6xl mb-4">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">Something went wrong</h2>
                <p class="text-gray-600 mb-6">${message}</p>
                <button onclick="window.location.reload()" class="bg-yellow-500 text-white px-6 py-3 rounded-lg hover:bg-yellow-600 transition-colors">
                    <i class="fas fa-refresh mr-2"></i>
                    Try Again
                </button>
            </div>
        `;
    }
}

/**
 * Debounce function for search input
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Export functions to global scope for PageRouter
window.initQuotesListPage = initQuotesListPage;
window.goToPage = goToPage;

// Export state for debugging
if (window.AppConfig && window.AppConfig.debug) {
    window.quotesListPageState = quotesListPageState;
}
