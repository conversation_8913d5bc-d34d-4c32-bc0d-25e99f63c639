<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Quote Navigation | Quotese.com</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link href="/css/variables.css" rel="stylesheet">
    <link href="/css/styles.css" rel="stylesheet">
    <link href="/css/responsive.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="container mx-auto px-4 py-6">
            <h1 class="text-3xl font-bold text-gray-800">
                <i class="fas fa-vial text-blue-500 mr-3"></i>
                Quote Navigation Test
            </h1>
            <p class="text-gray-600 mt-2">Testing quote card click navigation functionality</p>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Test Status -->
        <div id="test-status" class="mb-8 p-4 rounded-lg bg-blue-50 border border-blue-200">
            <h2 class="text-lg font-semibold text-blue-800 mb-2">
                <i class="fas fa-info-circle mr-2"></i>
                Test Status
            </h2>
            <div id="status-content">
                <p class="text-blue-700">Initializing test environment...</p>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="mb-8 p-4 rounded-lg bg-gray-50 border border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">
                <i class="fas fa-cogs mr-2"></i>
                Test Controls
            </h2>
            <div class="flex flex-wrap gap-4">
                <button id="test-navigation-enabled" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
                    <i class="fas fa-check mr-2"></i>
                    Test Navigation Enabled
                </button>
                <button id="test-navigation-disabled" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors">
                    <i class="fas fa-times mr-2"></i>
                    Test Navigation Disabled
                </button>
                <button id="reload-test" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                    <i class="fas fa-refresh mr-2"></i>
                    Reload Test
                </button>
            </div>
        </div>

        <!-- Test Results -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Navigation Enabled Test -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">
                    <i class="fas fa-mouse-pointer text-green-500 mr-2"></i>
                    Navigation Enabled (Clickable)
                </h3>
                <p class="text-gray-600 mb-4">These quote cards should be clickable and navigate to quote detail pages:</p>
                <div id="quotes-navigation-enabled" class="space-y-4">
                    <!-- Quotes with navigation enabled will be rendered here -->
                </div>
            </div>

            <!-- Navigation Disabled Test -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">
                    <i class="fas fa-ban text-red-500 mr-2"></i>
                    Navigation Disabled (Not Clickable)
                </h3>
                <p class="text-gray-600 mb-4">These quote cards should NOT be clickable:</p>
                <div id="quotes-navigation-disabled" class="space-y-4">
                    <!-- Quotes with navigation disabled will be rendered here -->
                </div>
            </div>
        </div>

        <!-- Test Log -->
        <div class="mt-8 bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
            <h3 class="text-white font-semibold mb-2">
                <i class="fas fa-terminal mr-2"></i>
                Test Log
            </h3>
            <div id="test-log" class="max-h-64 overflow-y-auto">
                <div class="text-gray-400">Test log will appear here...</div>
            </div>
        </div>
    </main>

    <!-- Core Scripts -->
    <script src="/js/theme.js?v=20250626"></script>
    <script src="/js/url-handler.js?v=20250626"></script>
    <script src="/js/api-client.js?v=20250626"></script>
    <script src="/js/components/quote-card.js?v=20250626"></script>

    <!-- Test Script -->
    <script>
        // Test data
        const testQuotes = [
            {
                id: 1,
                content: "The only way to do great work is to love what you do.",
                author: { id: 1, name: "Steve Jobs" },
                sources: [{ id: 1, name: "Stanford Commencement Address" }],
                categories: [{ id: 1, name: "Motivation" }, { id: 2, name: "Work" }]
            },
            {
                id: 2,
                content: "Innovation distinguishes between a leader and a follower.",
                author: { id: 1, name: "Steve Jobs" },
                sources: [{ id: 2, name: "Apple Keynote" }],
                categories: [{ id: 3, name: "Innovation" }, { id: 4, name: "Leadership" }]
            },
            {
                id: 3,
                content: "Life is what happens to you while you're busy making other plans.",
                author: { id: 2, name: "John Lennon" },
                sources: [{ id: 3, name: "Beautiful Boy" }],
                categories: [{ id: 5, name: "Life" }, { id: 6, name: "Philosophy" }]
            }
        ];

        // Test functions
        function logTest(message, type = 'info') {
            const testLog = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'text-red-400' : 
                              type === 'success' ? 'text-green-400' : 
                              type === 'warning' ? 'text-yellow-400' : 'text-blue-400';
            
            const logEntry = document.createElement('div');
            logEntry.className = colorClass;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            testLog.appendChild(logEntry);
            testLog.scrollTop = testLog.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const statusContent = document.getElementById('status-content');
            const iconClass = type === 'error' ? 'fas fa-times-circle text-red-500' : 
                             type === 'success' ? 'fas fa-check-circle text-green-500' : 
                             type === 'warning' ? 'fas fa-exclamation-triangle text-yellow-500' : 
                             'fas fa-info-circle text-blue-500';
            
            statusContent.innerHTML = `
                <p class="flex items-center">
                    <i class="${iconClass} mr-2"></i>
                    ${message}
                </p>
            `;
        }

        function testNavigationEnabled() {
            logTest('Testing navigation enabled quotes...', 'info');
            
            if (!window.QuoteCardComponent) {
                logTest('ERROR: QuoteCardComponent not found!', 'error');
                updateStatus('Test failed: QuoteCardComponent not available', 'error');
                return;
            }

            try {
                window.QuoteCardComponent.renderList(testQuotes, 'quotes-navigation-enabled', {
                    showAuthorAvatar: false,
                    showActions: false,
                    enableNavigation: true,
                    emptyMessage: 'No test quotes available'
                });
                
                logTest('✅ Navigation enabled quotes rendered successfully', 'success');
                updateStatus('Navigation enabled test completed successfully', 'success');
            } catch (error) {
                logTest(`ERROR rendering navigation enabled quotes: ${error.message}`, 'error');
                updateStatus('Test failed: Error rendering quotes', 'error');
            }
        }

        function testNavigationDisabled() {
            logTest('Testing navigation disabled quotes...', 'info');
            
            if (!window.QuoteCardComponent) {
                logTest('ERROR: QuoteCardComponent not found!', 'error');
                return;
            }

            try {
                window.QuoteCardComponent.renderList(testQuotes, 'quotes-navigation-disabled', {
                    showAuthorAvatar: false,
                    showActions: false,
                    enableNavigation: false,
                    emptyMessage: 'No test quotes available'
                });
                
                logTest('✅ Navigation disabled quotes rendered successfully', 'success');
            } catch (error) {
                logTest(`ERROR rendering navigation disabled quotes: ${error.message}`, 'error');
            }
        }

        function runAllTests() {
            logTest('=== Starting Quote Navigation Tests ===', 'info');
            
            // Check dependencies
            logTest('Checking dependencies...', 'info');
            logTest(`- UrlHandler: ${!!window.UrlHandler}`, window.UrlHandler ? 'success' : 'error');
            logTest(`- QuoteCardComponent: ${!!window.QuoteCardComponent}`, window.QuoteCardComponent ? 'success' : 'error');
            
            if (!window.UrlHandler || !window.QuoteCardComponent) {
                updateStatus('Test failed: Missing required dependencies', 'error');
                return;
            }
            
            // Run tests
            testNavigationEnabled();
            testNavigationDisabled();
            
            logTest('=== Quote Navigation Tests Completed ===', 'info');
        }

        // Event listeners
        document.getElementById('test-navigation-enabled').addEventListener('click', testNavigationEnabled);
        document.getElementById('test-navigation-disabled').addEventListener('click', testNavigationDisabled);
        document.getElementById('reload-test').addEventListener('click', () => {
            document.getElementById('test-log').innerHTML = '<div class="text-gray-400">Test log cleared...</div>';
            runAllTests();
        });

        // Initialize tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            logTest('Test page loaded, initializing...', 'info');
            updateStatus('Test environment initialized', 'success');
            
            // Wait a bit for all scripts to load
            setTimeout(runAllTests, 500);
        });
    </script>
</body>
</html>
